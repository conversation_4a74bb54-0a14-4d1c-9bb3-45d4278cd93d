<?php

namespace App\Filament\Resources\EmailTemplateResource\Pages;

use App\Filament\Resources\EmailTemplateResource;
use App\Services\VariableSubstitutionService;
use Filament\Actions;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\HtmlString;

class ViewEmailTemplate extends ViewRecord
{
    protected static string $resource = EmailTemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('preview')
                ->label('Preview Template')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->modalHeading('Template Preview')
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Close')
                ->modalContent(function () {
                    $template = $this->record;
                    $service = app(VariableSubstitutionService::class);
                    
                    // Generate sample data for preview
                    $sampleData = $service->createSampleData('all');
                    $processed = $template->processTemplateWithContext($sampleData);
                    
                    return view('filament.email-template-preview', [
                        'subject' => $processed['subject'],
                        'previewText' => $processed['preview_text'],
                        'body' => $processed['body'],
                        'variables' => $template->getVariables(),
                    ]);
                }),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Template Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large),
                        
                        Infolists\Components\IconEntry::make('is_active')
                            ->boolean()
                            ->label('Status'),
                        
                        Infolists\Components\TextEntry::make('creator.name')
                            ->label('Created By'),
                        
                        Infolists\Components\TextEntry::make('created_at')
                            ->dateTime(),
                        
                        Infolists\Components\TextEntry::make('updated_at')
                            ->dateTime(),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Email Content')
                    ->schema([
                        Infolists\Components\TextEntry::make('subject')
                            ->copyable(),
                        
                        Infolists\Components\TextEntry::make('preview_text')
                            ->copyable()
                            ->placeholder('No preview text set'),
                        
                        Infolists\Components\TextEntry::make('body')
                            ->html()
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Variables Used')
                    ->schema([
                        Infolists\Components\TextEntry::make('variable_display')
                            ->label('')
                            ->state(function () {
                                $variables = $this->record->getVariables();
                                if (empty($variables)) {
                                    return 'No variables used in this template.';
                                }

                                $html = '<div class="flex flex-wrap gap-1">';
                                foreach ($variables as $variable) {
                                    $html .= '<code class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 rounded">{' . $variable . '}</code>';
                                }
                                $html .= '</div>';

                                return new HtmlString($html);
                            })
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }
}
