<?php

namespace App\Filament\Resources\EmailTemplateResource\Pages;

use App\Filament\Resources\EmailTemplateResource;
use App\Services\VariableSubstitutionService;
use Filament\Actions;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\HtmlString;

class ViewEmailTemplate extends ViewRecord
{
    protected static string $resource = EmailTemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('preview')
                ->label('Enhanced Preview')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->modalHeading('Enhanced Template Preview')
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Close')
                ->modalWidth('7xl')
                ->modalContent(function () {
                    $template = $this->record;
                    $service = app(VariableSubstitutionService::class);

                    // Get available contexts
                    $contexts = [
                        'positive_athlete' => 'Positive Athlete',
                        'positive_coach' => 'Positive Coach',
                        'athletics_director' => 'Athletics Director',
                        'sponsor' => 'Sponsor',
                        'parent' => 'Parent',
                        'college_athlete' => 'College Athlete',
                        'professional' => 'Professional',
                        'nominee' => 'Nominee',
                        'contact' => 'Contact',
                    ];

                    // Generate sample data for default context (first one)
                    $defaultContext = array_key_first($contexts);
                    $sampleData = $service->createSampleData($defaultContext);
                    $processed = $template->processTemplateWithContext($sampleData);

                    return view('filament.email-template-preview', [
                        'templateId' => $template->id,
                        'contexts' => $contexts,
                        'subject' => $processed['subject'],
                        'previewText' => $processed['preview_text'],
                        'body' => $processed['body'],
                        'originalSubject' => $template->subject,
                        'originalPreviewText' => $template->preview_text,
                        'originalBody' => $template->body,
                        'variables' => $template->getVariables(),
                        'canSendTest' => config('messaging.send_external_messages', false),
                    ]);
                }),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Template Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large),
                        
                        Infolists\Components\IconEntry::make('is_active')
                            ->boolean()
                            ->label('Status'),
                        
                        Infolists\Components\TextEntry::make('creator.name')
                            ->label('Created By'),
                        
                        Infolists\Components\TextEntry::make('created_at')
                            ->dateTime(),
                        
                        Infolists\Components\TextEntry::make('updated_at')
                            ->dateTime(),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Email Content')
                    ->schema([
                        Infolists\Components\TextEntry::make('subject')
                            ->copyable(),
                        
                        Infolists\Components\TextEntry::make('preview_text')
                            ->copyable()
                            ->placeholder('No preview text set'),
                        
                        Infolists\Components\TextEntry::make('body')
                            ->html()
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Variables Used')
                    ->schema([
                        Infolists\Components\TextEntry::make('variable_display')
                            ->label('')
                            ->state(function () {
                                $variables = $this->record->getVariables();
                                if (empty($variables)) {
                                    return 'No variables used in this template.';
                                }

                                $html = '<div class="flex flex-wrap gap-1">';
                                foreach ($variables as $variable) {
                                    $html .= '<code class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 rounded">{' . $variable . '}</code>';
                                }
                                $html .= '</div>';

                                return new HtmlString($html);
                            })
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }
}
