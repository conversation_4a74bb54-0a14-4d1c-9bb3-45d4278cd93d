<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EmailTemplateResource\Pages;
use App\Models\EmailTemplate;
use App\Services\VariableSubstitutionService;
use App\Notifications\MultiChannelNotification;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

class EmailTemplateResource extends Resource
{
    protected static ?string $model = EmailTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Template Details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn (string $context, $state, Forms\Set $set) => 
                                $context === 'create' ? $set('subject', $state) : null
                            ),

                        Forms\Components\Toggle::make('is_active')
                            ->default(true)
                            ->helperText('Inactive templates cannot be used for sending messages'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Email Content')
                    ->schema([
                        Forms\Components\TextInput::make('subject')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Supports variables like {user.firstname}, {nominee.name}, {system.app_name}'),

                        Forms\Components\Textarea::make('preview_text')
                            ->maxLength(255)
                            ->rows(2)
                            ->helperText('Optional preview text shown in email clients. Supports variables.'),

                        Forms\Components\RichEditor::make('body')
                            ->required()
                            ->columnSpanFull()
                            ->helperText('Email body content. Supports variables and rich formatting.')
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'underline',
                                'strike',
                                'link',
                                'bulletList',
                                'orderedList',
                                'h2',
                                'h3',
                                'blockquote',
                                'codeBlock',
                            ]),
                    ]),

                Forms\Components\Section::make('Variable Helper')
                    ->schema([
                        Forms\Components\Placeholder::make('variable_picker')
                            ->label('Available Variables')
                            ->content(function () {
                                $service = app(VariableSubstitutionService::class);
                                $contextData = $service->getVariablesByContext();

                                $html = '<div x-data="variablePicker()" class="space-y-4">';
                                $html .= '<p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Click any variable to copy it to your clipboard, then paste it into your template.</p>';

                                foreach ($contextData as $data) {
                                    $html .= '<div class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">';
                                    $html .= '<h4 class="font-semibold text-sm text-gray-800 dark:text-gray-200 mb-1">' . $data['label'] . '</h4>';
                                    $html .= '<p class="text-xs text-gray-500 dark:text-gray-400 mb-2">' . $data['description'] . '</p>';
                                    $html .= '<div class="grid grid-cols-1 md:grid-cols-2 gap-1">';

                                    foreach ($data['variables'] as $variable => $description) {
                                        $fullVariable = '{' . $variable . '}';
                                        $html .= '<div @click="copyVariable(\'' . htmlspecialchars($fullVariable, ENT_QUOTES) . '\')" class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer group" title="Click to copy">';
                                        $html .= '<div class="flex-1 min-w-0">';
                                        $html .= '<code class="text-xs font-mono text-blue-600 dark:text-blue-400">' . htmlspecialchars($fullVariable) . '</code>';
                                        $html .= '<div class="text-xs text-gray-500 dark:text-gray-400 truncate">' . htmlspecialchars($description) . '</div>';
                                        $html .= '</div>';
                                        $html .= '<svg class="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                                        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>';
                                        $html .= '</svg>';
                                        $html .= '</div>';
                                    }

                                    $html .= '</div></div>';
                                }

                                // Notification area
                                $html .= '<div x-show="notification.show" x-transition class="fixed top-4 right-4 px-4 py-2 rounded shadow-lg z-50" :class="notification.type === \'success\' ? \'bg-green-500 text-white\' : \'bg-red-500 text-white\'" x-text="notification.message"></div>';

                                $html .= '</div>';

                                // Add Alpine.js component
                                $html .= '<script>
                                    function variablePicker() {
                                        return {
                                            notification: {
                                                show: false,
                                                message: "",
                                                type: "success"
                                            },

                                            copyVariable(variable) {
                                                this.copyToClipboard(variable);
                                            },

                                            async copyToClipboard(text) {
                                                try {
                                                    if (navigator.clipboard && window.isSecureContext) {
                                                        await navigator.clipboard.writeText(text);
                                                        this.showNotification("Copied: " + text, "success");
                                                    } else {
                                                        this.fallbackCopy(text);
                                                    }
                                                } catch (err) {
                                                    console.error("Copy failed:", err);
                                                    this.fallbackCopy(text);
                                                }
                                            },

                                            fallbackCopy(text) {
                                                const textArea = document.createElement("textarea");
                                                textArea.value = text;
                                                textArea.style.position = "fixed";
                                                textArea.style.left = "-999999px";
                                                textArea.style.top = "-999999px";
                                                document.body.appendChild(textArea);
                                                textArea.focus();
                                                textArea.select();

                                                try {
                                                    const successful = document.execCommand("copy");
                                                    if (successful) {
                                                        this.showNotification("Copied: " + text, "success");
                                                    } else {
                                                        this.showNotification("Copy failed. Please select and copy manually: " + text, "error");
                                                    }
                                                } catch (err) {
                                                    console.error("Fallback copy failed:", err);
                                                    this.showNotification("Copy failed. Please select and copy manually: " + text, "error");
                                                }

                                                document.body.removeChild(textArea);
                                            },

                                            showNotification(message, type) {
                                                this.notification = {
                                                    show: true,
                                                    message: message,
                                                    type: type
                                                };

                                                setTimeout(() => {
                                                    this.notification.show = false;
                                                }, 3000);
                                            }
                                        }
                                    }
                                </script>';

                                return new \Illuminate\Support\HtmlString($html);
                            })
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),

                Forms\Components\Hidden::make('created_by')
                    ->default(fn () => Auth::id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('subject')
                    ->searchable()
                    ->limit(50),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmailTemplates::route('/'),
            'create' => Pages\CreateEmailTemplate::route('/create'),
            'view' => Pages\ViewEmailTemplate::route('/{record}'),
            'edit' => Pages\EditEmailTemplate::route('/{record}/edit'),
        ];
    }

    /**
     * Register additional routes for enhanced preview functionality.
     */
    public static function registerRoutes(): void
    {
        Route::post('/admin/email-templates/preview', [static::class, 'previewTemplate'])
            ->name('filament.admin.resources.email-templates.preview')
            ->middleware(['web', 'auth']);

        Route::post('/admin/email-templates/validate', [static::class, 'validateTemplate'])
            ->name('filament.admin.resources.email-templates.validate')
            ->middleware(['web', 'auth']);

        Route::post('/admin/email-templates/send-test', [static::class, 'sendTestEmail'])
            ->name('filament.admin.resources.email-templates.send-test')
            ->middleware(['web', 'auth']);
    }

    /**
     * Preview template with context-specific sample data.
     */
    public static function previewTemplate(Request $request): JsonResponse
    {
        $request->validate([
            'template_id' => 'nullable|exists:email_templates,id',
            'context' => 'required|string',
            'subject' => 'required|string',
            'preview_text' => 'nullable|string',
            'body' => 'required|string',
        ]);

        $service = app(VariableSubstitutionService::class);

        // Generate sample data for the specified context
        $sampleData = $service->createSampleData($request->context);

        // Create a temporary template object for processing
        $tempTemplate = new EmailTemplate([
            'subject' => $request->subject,
            'preview_text' => $request->preview_text,
            'body' => $request->body,
        ]);

        // Process the template with context data
        $processed = $tempTemplate->processTemplateWithContext($sampleData);

        // Validate variables
        $variables = $tempTemplate->getVariables();
        $missingVariables = $tempTemplate->validateVariables($sampleData);

        return response()->json([
            'subject' => $processed['subject'],
            'preview_text' => $processed['preview_text'],
            'body' => $processed['body'],
            'validation' => [
                'valid' => empty($missingVariables),
                'missingVariables' => $missingVariables,
                'allVariables' => $variables,
            ],
        ]);
    }

    /**
     * Validate template variables for a specific context.
     */
    public static function validateTemplate(Request $request): JsonResponse
    {
        $request->validate([
            'template_id' => 'nullable|exists:email_templates,id',
            'context' => 'required|string',
            'subject' => 'required|string',
            'preview_text' => 'nullable|string',
            'body' => 'required|string',
        ]);

        $service = app(VariableSubstitutionService::class);

        // Generate sample data for the specified context
        $sampleData = $service->createSampleData($request->context);

        // Create a temporary template object for validation
        $tempTemplate = new EmailTemplate([
            'subject' => $request->subject,
            'preview_text' => $request->preview_text,
            'body' => $request->body,
        ]);

        // Get all variables and validate them
        $variables = $tempTemplate->getVariables();
        $missingVariables = $tempTemplate->validateVariables($sampleData);

        return response()->json([
            'valid' => empty($missingVariables),
            'missingVariables' => $missingVariables,
            'allVariables' => $variables,
            'context' => $request->context,
        ]);
    }

    /**
     * Send a test email using the template.
     */
    public static function sendTestEmail(Request $request): JsonResponse
    {
        $request->validate([
            'template_id' => 'required|exists:email_templates,id',
            'context' => 'required|string',
        ]);

        try {
            $template = EmailTemplate::findOrFail($request->template_id);
            $service = app(VariableSubstitutionService::class);

            // Generate sample data for the specified context
            $sampleData = $service->createSampleData($request->context);

            // Get the current user's email for test sending
            $testEmail = config('messaging.environment.dev_recipient_override')
                ?? Auth::user()->email;

            // Create a test notification
            $notification = new class($template, $sampleData) extends MultiChannelNotification {
                public function __construct(
                    private EmailTemplate $template,
                    private array $sampleData
                ) {
                    parent::__construct($template, $sampleData);
                }

                protected function shouldSendEmail($notifiable): bool
                {
                    return true; // Always send for test
                }

                protected function shouldSendSms($notifiable): bool
                {
                    return false; // Never send SMS for test
                }

                protected function getContextualTemplateData($notifiable): array
                {
                    return $this->sampleData;
                }
            };

            // Create a test user object for notification
            $testUser = new class {
                public function routeNotificationFor($channel)
                {
                    if ($channel === 'mail') {
                        return config('messaging.environment.dev_recipient_override')
                            ?? Auth::user()->email;
                    }
                    return null;
                }

                public function getKey()
                {
                    return Auth::id();
                }
            };

            // Send the test notification
            $testUser->notify($notification);

            return response()->json([
                'success' => true,
                'message' => "Test email sent to {$testEmail}",
                'context' => $request->context,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage(),
            ], 500);
        }
    }
}
