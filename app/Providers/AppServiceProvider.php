<?php

namespace App\Providers;

use App\Models\User;
use App\Models\Opportunity;
use App\Observers\UserObserver;
use App\Observers\OpportunityObserver;
use App\Repositories\ImportRepository;
use App\Services\Import\Tracking\ImportTrackingService;
use App\Filament\Resources\EmailTemplateResource;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Foundation\AliasLoader;
use Illuminate\Support\Facades\Gate;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Import services
        $this->app->scoped(ImportTrackingService::class, function ($app) {
            return new ImportTrackingService();
        });

        $this->app->scoped(ImportRepository::class, function ($app) {
            return new ImportRepository();
        });

        // Register the EloquentSerialize facade alias
        $loader = AliasLoader::getInstance();
        $loader->alias('EloquentSerialize', \AnourValar\EloquentSerialize\Facades\EloquentSerializeFacade::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Boot the User model to ensure its filters are registered
        // We need to create User instances with different profile types
        // to ensure both 'users' and 'athletes' resource types are registered

        $userModel = app(\App\Models\User::class);

        // For creating an athlete user, set the profile type
        $athleteUser = new \App\Models\User();
        $athleteUser->profile_type = \App\Enums\ProfileType::POSITIVE_ATHLETE;
        app()->instance('athlete_user_for_filters', $athleteUser);

        VerifyEmail::createUrlUsing(function ($notifiable) {
            $id = $notifiable->getKey();
            $hash = sha1($notifiable->getEmailForVerification());
            $expires = Carbon::now()->addMinutes(Config::get('auth.verification.expire', 60))->getTimestamp();

            $signature = hash_hmac('sha256', $id . $hash . $expires, config('app.key'));

            return config('app.frontend_url') . '/verify-email?' . http_build_query([
                'id' => $id,
                'hash' => $hash,
                'expires' => $expires,
                'signature' => $signature
            ]);
        });

        ResetPassword::createUrlUsing(function (object $notifiable, string $token) {
            return config('app.frontend_url') . "/password-reset/$token?email={$notifiable->getEmailForPasswordReset()}";
        });

        $this->rateLimits();
        $this->registerObservers();
        $this->registerPolicies();
        $this->registerEmailTemplateRoutes();
    }

    private function rateLimits(): void
    {
        RateLimiter::for('nominations', function (Request $request) {
            return Limit::perMinute(10)->by($request->email);
        });

        RateLimiter::for('endorsements', function (Request $request) {
            return Limit::perHour(50)->by($request->input('userId:') .  $request->ip());
        });

        // Webhook rate limiters
        RateLimiter::for('webhook-receive', function (Request $request) {
            // More lenient for legitimate submission surges from JotForm
            return Limit::perMinute(100)->by($request->ip())
                ->response(function () {
                    return response()->json([
                        'error' => 'Too many webhook requests. Please slow down.'
                    ], 429);
                });
        });

        RateLimiter::for('webhook-admin', function (Request $request) {
            // Stricter for administrative webhook endpoints
            return Limit::perMinute(60)->by($request->user()->id ?? $request->ip());
        });
    }

    private function registerObservers(): void
    {
        User::observe(UserObserver::class);
        Opportunity::observe(OpportunityObserver::class);
    }

    private function registerPolicies(): void
    {
        // Register custom policies that don't follow Laravel's naming conventions
        Gate::policy(\App\Models\User::class, \App\Policies\PublicProfilePolicy::class);
    }

    private function registerEmailTemplateRoutes(): void
    {
        // Register enhanced email template preview routes
        EmailTemplateResource::registerRoutes();
    }
}
