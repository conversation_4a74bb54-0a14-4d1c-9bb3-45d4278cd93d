<div class="space-y-6" x-data="emailPreview()">
    <!-- Context Selector -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Preview Context</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
            @foreach($allContexts as $contextKey => $contextLabel)
                <button
                    type="button"
                    @click="switchContext('{{ $contextKey }}')"
                    :class="currentContext === '{{ $contextKey }}' ? 'bg-blue-600 text-white' : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'"
                    class="px-3 py-2 text-xs font-medium rounded-md transition-colors"
                >
                    {{ $contextLabel }}
                </button>
            @endforeach
        </div>
    </div>

    <!-- Email Preview with Full Branding -->
    <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Email Preview</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400">Full branded email as recipients will see it</p>
            </div>
            @if(isset($canSendTest) && $canSendTest)
                <button
                    type="button"
                    @click="sendTestEmail()"
                    :disabled="sendingTest"
                    class="px-3 py-1 text-xs bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
                >
                    <span x-show="!sendingTest">Send Test</span>
                    <span x-show="sendingTest">Sending...</span>
                </button>
            @endif
        </div>

        <!-- Email Client Mockup -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-lg">
            <!-- Email Client Header -->
            <div class="bg-gray-100 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                <div class="flex items-center gap-3">
                    <div class="flex gap-1">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Email Client</div>
                </div>
            </div>

            <!-- Email Metadata -->
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                <div class="space-y-1">
                    <div class="text-sm">
                        <span class="font-medium text-gray-700 dark:text-gray-300">From:</span>
                        <span class="text-gray-900 dark:text-gray-100">{{ config('mail.from.name') }} &lt;{{ config('mail.from.address') }}&gt;</span>
                    </div>
                    <div class="text-sm">
                        <span class="font-medium text-gray-700 dark:text-gray-300">Subject:</span>
                        <span class="text-gray-900 dark:text-gray-100" x-text="getCurrentProcessedField('subject')">{{ $processedTemplates[array_key_first($processedTemplates)]['subject'] }}</span>
                    </div>
                    @if($processedTemplates[array_key_first($processedTemplates)]['preview_text'])
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            <span class="font-medium">Preview:</span>
                            <span x-text="getCurrentProcessedField('preview_text')">{{ $processedTemplates[array_key_first($processedTemplates)]['preview_text'] }}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Actual Email Template Preview -->
            <div class="overflow-hidden">
                @foreach($processedTemplates as $contextKey => $processed)
                    <div x-show="currentContext === '{{ $contextKey }}'" class="w-full">
                        <!-- Render the actual email template using Laravel's mail markdown -->
                        <x-mail::message>
                            {{-- Use the processed template content --}}
                            {!! $processed['body'] !!}
                        </x-mail::message>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Template Validation & Variables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- Variables Used -->
        @if(!empty($variables))
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Variables Used in This Template</h4>
                <div class="flex flex-wrap gap-1 mb-3">
                    @foreach($variables as $variable)
                        <code class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">{{ '{' . $variable . '}' }}</code>
                    @endforeach
                </div>
                <p class="text-xs text-blue-600 dark:text-blue-300">
                    These variables will be replaced with actual data when the email is sent.
                </p>
            </div>
        @endif

        <!-- Template Validation -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-lg">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Template Validation</h4>
            <div x-show="validationResults" class="space-y-2">
                <div x-show="validationResults?.valid" class="flex items-center gap-2 text-green-600">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm">Template is valid</span>
                </div>
                <div x-show="validationResults?.missingVariables?.length > 0" class="space-y-1">
                    <div class="flex items-center gap-2 text-yellow-600">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm">Missing variables for current context</span>
                    </div>
                    <template x-for="variable in validationResults?.missingVariables">
                        <code x-text="variable" class="block px-2 py-1 text-xs bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded"></code>
                    </template>
                </div>
            </div>
            <div x-show="!validationResults" class="text-sm text-gray-500 dark:text-gray-400">
                Select a context to validate template variables
            </div>
        </div>
    </div>
</div>

<script>
function emailPreview() {
    return {
        currentContext: '{{ array_key_first($contexts ?? []) }}',
        currentSubject: @js($subject),
        currentPreviewText: @js($previewText ?? ''),
        currentBody: @js($body),
        validationResults: null,
        sendingTest: false,

        init() {
            this.validateTemplate();
        },

        switchContext(context) {
            this.currentContext = context;
            this.refreshPreview();
        },

        async refreshPreview() {
            try {
                const response = await fetch('{{ route("filament.admin.resources.email-templates.preview") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        template_id: {{ $templateId ?? 'null' }},
                        context: this.currentContext,
                        subject: @js($originalSubject ?? ''),
                        preview_text: @js($originalPreviewText ?? ''),
                        body: @js($originalBody ?? '')
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    this.currentSubject = data.subject;
                    this.currentPreviewText = data.preview_text;
                    this.currentBody = data.body;
                    this.validationResults = data.validation;
                }
            } catch (error) {
                console.error('Failed to refresh preview:', error);
            }
        },

        async validateTemplate() {
            if (!this.currentContext) return;

            try {
                const response = await fetch('{{ route("filament.admin.resources.email-templates.validate") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        template_id: {{ $templateId ?? 'null' }},
                        context: this.currentContext,
                        subject: @js($originalSubject ?? ''),
                        preview_text: @js($originalPreviewText ?? ''),
                        body: @js($originalBody ?? '')
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    this.validationResults = data;
                }
            } catch (error) {
                console.error('Failed to validate template:', error);
            }
        },

        async sendTestEmail() {
            if (this.sendingTest) return;

            this.sendingTest = true;

            try {
                const response = await fetch('{{ route("filament.admin.resources.email-templates.send-test") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        template_id: {{ $templateId ?? 'null' }},
                        context: this.currentContext
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    // Show success notification
                    new FilamentNotification()
                        .title('Test email sent successfully')
                        .success()
                        .send();
                } else {
                    // Show error notification
                    new FilamentNotification()
                        .title('Failed to send test email')
                        .body(data.message || 'An error occurred')
                        .danger()
                        .send();
                }
            } catch (error) {
                console.error('Failed to send test email:', error);
                new FilamentNotification()
                    .title('Failed to send test email')
                    .body('Network error occurred')
                    .danger()
                    .send();
            } finally {
                this.sendingTest = false;
            }
        }
    }
}
</script>
