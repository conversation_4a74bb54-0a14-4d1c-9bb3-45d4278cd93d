<div class="space-y-6">
    <!-- Template Preview Header -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Email Template Preview</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400">Preview shows the email with highlighted variable placeholders</p>
            </div>
            @if(isset($canSendTest) && $canSendTest)
                <form method="POST" action="{{ route('filament.admin.resources.email-templates.send-test') }}" class="inline">
                    @csrf
                    <input type="hidden" name="template_id" value="{{ $templateId }}">
                    <button
                        type="submit"
                        class="px-3 py-1 text-xs bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                    >
                        Send Test Email
                    </button>
                </form>
            @endif
        </div>
    </div>

    <!-- Email Preview -->
    <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Email Preview</h3>

        <!-- Email Preview using actual Laravel mail template -->
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-lg">
            <!-- Email Header Info -->
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <div class="space-y-1">
                    <div class="text-sm">
                        <span class="font-medium text-gray-700">From:</span>
                        <span class="text-gray-900">{{ config('mail.from.name') }} &lt;{{ config('mail.from.address') }}&gt;</span>
                    </div>
                    <div class="text-sm">
                        <span class="font-medium text-gray-700">Subject:</span>
                        <span class="text-gray-900">{!! $highlightedSubject !!}</span>
                    </div>
                    @if($highlightedPreviewText)
                        <div class="text-xs text-gray-500">
                            <span class="font-medium">Preview:</span>
                            <span>{!! $highlightedPreviewText !!}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Email Body using Laravel Mail Template -->
            <div class="email-preview-content">
                <x-mail::message>
                    {!! $highlightedBody !!}
                </x-mail::message>
            </div>
        </div>
    </div>

    <!-- Variables Used -->
    @if(!empty($variables))
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Variables Used in This Template</h4>
            <div class="flex flex-wrap gap-1 mb-3">
                @foreach($variables as $variable)
                    <code class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">{{ '{' . $variable . '}' }}</code>
                @endforeach
            </div>
            <p class="text-xs text-blue-600 dark:text-blue-300">
                Variables are highlighted in the preview above and will be replaced with actual data when emails are sent.
            </p>
        </div>
    @endif
</div>

<style>
.email-preview-content .variable-highlight {
    background-color: #fef3c7;
    color: #92400e;
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 600;
    border: 1px solid #f59e0b;
}

.dark .email-preview-content .variable-highlight {
    background-color: #451a03;
    color: #fbbf24;
    border-color: #d97706;
}
</style>


