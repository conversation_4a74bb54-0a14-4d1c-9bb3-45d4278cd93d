<div class="space-y-6" x-data="emailPreview()">
    <!-- Context Selector -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Preview Context</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
            @foreach($contexts as $contextKey => $contextLabel)
                <button
                    type="button"
                    @click="switchContext('{{ $contextKey }}')"
                    :class="currentContext === '{{ $contextKey }}' ? 'bg-blue-600 text-white' : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'"
                    class="px-3 py-2 text-xs font-medium rounded-md transition-colors"
                >
                    {{ $contextLabel }}
                </button>
            @endforeach
        </div>
    </div>

    <!-- Email Preview with Full Branding -->
    <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Email Preview</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400">Full branded email as recipients will see it</p>
            </div>
            <div class="flex gap-2">
                <button
                    type="button"
                    @click="refreshPreview()"
                    class="px-3 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                    Refresh
                </button>
                @if(isset($canSendTest) && $canSendTest)
                    <button
                        type="button"
                        @click="sendTestEmail()"
                        :disabled="sendingTest"
                        class="px-3 py-1 text-xs bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
                    >
                        <span x-show="!sendingTest">Send Test</span>
                        <span x-show="sendingTest">Sending...</span>
                    </button>
                @endif
            </div>
        </div>

        <!-- Email Client Mockup -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-lg">
            <!-- Email Client Header -->
            <div class="bg-gray-100 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                <div class="flex items-center gap-3">
                    <div class="flex gap-1">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Email Client</div>
                </div>
            </div>

            <!-- Email Metadata -->
            <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                <div class="space-y-1">
                    <div class="text-sm">
                        <span class="font-medium text-gray-700 dark:text-gray-300">From:</span>
                        <span class="text-gray-900 dark:text-gray-100">{{ config('mail.from.name') }} &lt;{{ config('mail.from.address') }}&gt;</span>
                    </div>
                    <div class="text-sm">
                        <span class="font-medium text-gray-700 dark:text-gray-300">Subject:</span>
                        <span class="text-gray-900 dark:text-gray-100" x-text="currentSubject">{{ $subject }}</span>
                    </div>
                    @if($previewText)
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            <span class="font-medium">Preview:</span>
                            <span x-text="currentPreviewText">{{ $previewText }}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Full Branded Email Content -->
            <div class="bg-gray-100" style="background-color: #F6F6F6; padding: 64px 0; font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;">
                <div style="max-width: 640px; margin: 0 auto; background-color: #ffffff; border-radius: 32px; box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.04); overflow: hidden;">
                    <!-- Email Header with Logo -->
                    <div style="padding: 56px 56px 0 56px;">
                        <div style="margin-bottom: 40px;">
                            <a href="{{ config('app.frontend_url') }}" style="display: inline-block;">
                                <svg width="160" height="43" viewBox="0 0 160 43" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_2001_1251)">
                                        <path d="M4.93195 42.1978H10.2997V37.245H13.1222L16.1268 31.8545H10.2998V26.8925H4.93203V31.8545H0V37.245H4.93195V42.1978Z" fill="#002855"/>
                                        <path d="M37.3779 23.0556H44.6412V36.1052H49.2936V23.0556H56.5877V19.2455H37.3779V23.0556Z" fill="#002855"/>
                                        <path d="M72.5756 25.4011H63.3572V19.2455H58.7051V36.1052H63.3572V29.2727H72.5756V36.1052H77.2582V19.2455H72.5756V25.4011Z" fill="#002855"/>
                                        <path d="M85.0277 19.2455H80.3752V36.1052H96.8881V32.1412H85.0277V19.2455Z" fill="#002855"/>
                                        <path d="M104.877 29.1803H112.171V25.4932H104.877V22.9324H117.382V19.2455H100.225V36.1052H117.535V32.1412H104.877V29.1803Z" fill="#002855"/>
                                        <path d="M120.969 23.0556H128.233V36.1052H132.885V23.0556H140.179V19.2455H120.969V23.0556Z" fill="#002855"/>
                                        <path d="M147.343 32.1412V29.1803H154.637V25.4932H147.343V22.9324H159.847V19.2455H142.69V36.1052H160V32.1412H147.343Z" fill="#002855"/>
                                        <path d="M45.2593 42.1978L31.4144 17.9606H25.9886L12.3308 42.1978H17.9437L20.75 36.8899L23.1354 32.5216L28.5144 22.6576L33.3549 31.8544H25.5869L22.5824 37.2449H36.2388L38.945 42.1978H45.2593Z" fill="#002855"/>
                                        <path d="M50.531 0H38.885C37.253 0 36.138 0.293415 35.477 0.896382C34.8026 1.51212 34.4746 2.60731 34.4746 4.24394V12.6156C34.4746 14.2532 34.8026 15.3481 35.4766 15.9629C36.1373 16.5665 37.2523 16.8599 38.885 16.8599H50.531C52.163 16.8599 53.2777 16.5665 53.9387 15.9632C54.6131 15.3478 54.9411 14.2527 54.9411 12.6157V4.24401C54.9411 2.60767 54.6134 1.51283 53.9387 0.896454C53.277 0.293128 52.1624 0 50.531 0ZM50.289 3.81009V12.896H39.127V3.81009H50.289Z" fill="#D50032"/>
                                        <path d="M72.0902 6.18616H62.709V3.74868H72.0934V5.69343L76.3166 4.69319V4.30582C76.3166 2.65227 75.996 1.54438 75.3365 0.918749C74.6849 0.300433 73.563 6.10352e-05 71.9065 6.10352e-05H62.9265C61.2945 6.10352e-05 60.1799 0.293476 59.5188 0.896443C58.8445 1.51189 58.5164 2.60701 58.5164 4.244V5.99853C58.5164 7.63588 58.8444 8.731 59.5185 9.3458C60.1792 9.94913 61.2938 10.2425 62.9265 10.2425H72.3077V12.896H62.0961V10.9067L57.8728 11.7865V12.1847C57.8728 13.9676 58.164 15.1618 58.7625 15.8343C59.3754 16.5245 60.537 16.86 62.3136 16.86H72.0901C73.7208 16.86 74.8393 16.5676 75.5102 15.9661C76.1969 15.35 76.5309 14.2542 76.5309 12.6157V10.4305C76.5309 8.79282 76.1972 7.6967 75.5098 7.08003C74.8386 6.47864 73.72 6.18616 72.0902 6.18616Z" fill="#D50032"/>
                                        <path d="M85.2414 0H80.5894V16.8599H85.2414V0Z" fill="#D50032"/>
                                        <path d="M88.6096 3.81009H95.8729V16.8599H100.525V3.81009H107.819V0H88.6096V3.81009Z" fill="#D50032"/>
                                        <path d="M115.569 0H110.917V16.8599H115.569V0Z" fill="#D50032"/>
                                        <path d="M129.641 10.9497L123.532 0H118.204L127.899 16.8599H131.012L140.253 0H135.458L129.641 10.9497Z" fill="#D50032"/>
                                        <path d="M147.343 12.896V9.93479H154.637V6.24792H147.343V3.68716H159.847V0H142.69V16.8599H160V12.896H147.343Z" fill="#D50032"/>
                                        <path d="M25.6059 0.489349H4.93188V4.6696H23.08C23.9526 4.6696 24.5606 4.77983 24.9042 4.99837C25.3718 5.2494 25.6058 5.78165 25.6058 6.59555V9.17898C25.6058 9.99381 25.3717 10.5258 24.9042 10.7762C24.5606 10.9953 23.9526 11.1049 23.08 11.1049H10.4512V6.46372H4.93188V25.1343H10.4512V15.2852H25.6058C29.2226 15.2852 31.0315 13.7198 31.0315 10.5882V5.18635C31.0315 2.0554 29.2226 0.489349 25.6059 0.489349Z" fill="#D50032"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_2001_1251">
                                            <rect width="160" height="42.1978" fill="white"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Email Body Content -->
                    <div style="padding: 0 56px;">
                        <div x-html="currentBody" style="color: #383838; font-size: 16px; line-height: 1.5;">
                            {!! $body !!}
                        </div>
                    </div>

                    <!-- Email Footer -->
                    <div style="padding: 56px; text-align: center; border-top: 1px solid #EDEFF0; margin-top: 25px;">
                        <p style="color: #777D80; font-size: 14px; margin: 0;">
                            © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Validation & Variables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- Variables Used -->
        @if(!empty($variables))
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Variables Used in This Template</h4>
                <div class="flex flex-wrap gap-1 mb-3">
                    @foreach($variables as $variable)
                        <code class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">{{ '{' . $variable . '}' }}</code>
                    @endforeach
                </div>
                <p class="text-xs text-blue-600 dark:text-blue-300">
                    These variables will be replaced with actual data when the email is sent.
                </p>
            </div>
        @endif

        <!-- Template Validation -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-lg">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Template Validation</h4>
            <div x-show="validationResults" class="space-y-2">
                <div x-show="validationResults?.valid" class="flex items-center gap-2 text-green-600">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm">Template is valid</span>
                </div>
                <div x-show="validationResults?.missingVariables?.length > 0" class="space-y-1">
                    <div class="flex items-center gap-2 text-yellow-600">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm">Missing variables for current context</span>
                    </div>
                    <template x-for="variable in validationResults?.missingVariables">
                        <code x-text="variable" class="block px-2 py-1 text-xs bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded"></code>
                    </template>
                </div>
            </div>
            <div x-show="!validationResults" class="text-sm text-gray-500 dark:text-gray-400">
                Select a context to validate template variables
            </div>
        </div>
    </div>
</div>

<script>
function emailPreview() {
    return {
        currentContext: '{{ array_key_first($contexts ?? []) }}',
        currentSubject: @js($subject),
        currentPreviewText: @js($previewText ?? ''),
        currentBody: @js($body),
        validationResults: null,
        sendingTest: false,

        init() {
            this.validateTemplate();
        },

        switchContext(context) {
            this.currentContext = context;
            this.refreshPreview();
        },

        async refreshPreview() {
            try {
                const response = await fetch('{{ route("filament.admin.resources.email-templates.preview") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        template_id: {{ $templateId ?? 'null' }},
                        context: this.currentContext,
                        subject: @js($originalSubject ?? ''),
                        preview_text: @js($originalPreviewText ?? ''),
                        body: @js($originalBody ?? '')
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    this.currentSubject = data.subject;
                    this.currentPreviewText = data.preview_text;
                    this.currentBody = data.body;
                    this.validationResults = data.validation;
                }
            } catch (error) {
                console.error('Failed to refresh preview:', error);
            }
        },

        async validateTemplate() {
            if (!this.currentContext) return;

            try {
                const response = await fetch('{{ route("filament.admin.resources.email-templates.validate") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        template_id: {{ $templateId ?? 'null' }},
                        context: this.currentContext,
                        subject: @js($originalSubject ?? ''),
                        preview_text: @js($originalPreviewText ?? ''),
                        body: @js($originalBody ?? '')
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    this.validationResults = data;
                }
            } catch (error) {
                console.error('Failed to validate template:', error);
            }
        },

        async sendTestEmail() {
            if (this.sendingTest) return;

            this.sendingTest = true;

            try {
                const response = await fetch('{{ route("filament.admin.resources.email-templates.send-test") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        template_id: {{ $templateId ?? 'null' }},
                        context: this.currentContext
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    // Show success notification
                    new FilamentNotification()
                        .title('Test email sent successfully')
                        .success()
                        .send();
                } else {
                    // Show error notification
                    new FilamentNotification()
                        .title('Failed to send test email')
                        .body(data.message || 'An error occurred')
                        .danger()
                        .send();
                }
            } catch (error) {
                console.error('Failed to send test email:', error);
                new FilamentNotification()
                    .title('Failed to send test email')
                    .body('Network error occurred')
                    .danger()
                    .send();
            } finally {
                this.sendingTest = false;
            }
        }
    }
}
</script>
