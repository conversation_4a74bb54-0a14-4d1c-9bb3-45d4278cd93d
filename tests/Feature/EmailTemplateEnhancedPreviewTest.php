<?php

use App\Models\EmailTemplate;
use App\Models\User;
use App\Services\VariableSubstitutionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create admin role if it doesn't exist
    Role::firstOrCreate(['name' => 'admin']);

    $this->adminUser = User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    $this->adminUser->assignRole('admin');
});

it('can generate context-specific sample data', function () {
    $service = app(VariableSubstitutionService::class);
    
    // Test positive athlete context
    $athleteData = $service->createSampleData('positive_athlete');
    expect($athleteData['user']['profile_type'])->toBe('positive_athlete');
    expect($athleteData['user']['first_name'])->toBe('Alex');
    expect($athleteData['user']['life_stage'])->toBe('high_school');
    
    // Test positive coach context
    $coachData = $service->createSampleData('positive_coach');
    expect($coachData['user']['profile_type'])->toBe('positive_coach');
    expect($coachData['user']['first_name'])->toBe('Sarah');
    expect($coachData['user']['life_stage'])->toBe('professional');
});

it('can preview template with different contexts', function () {
    $this->actingAs($this->adminUser);
    
    $template = EmailTemplate::factory()->create([
        'name' => 'Welcome Email',
        'subject' => 'Welcome {user.first_name}!',
        'preview_text' => 'Welcome to {system.app_name}',
        'body' => '<p>Hello {user.first_name} {user.last_name},</p><p>Welcome to our platform!</p>',
        'created_by' => $this->adminUser->id,
    ]);
    
    $response = $this->postJson('/admin/email-templates/preview', [
        'template_id' => $template->id,
        'context' => 'positive_athlete',
        'subject' => $template->subject,
        'preview_text' => $template->preview_text,
        'body' => $template->body,
    ]);
    
    $response->assertOk();
    $data = $response->json();
    
    expect($data['subject'])->toBe('Welcome Alex!');
    expect($data['body'])->toContain('Hello Alex Johnson');
    expect($data['validation']['valid'])->toBeTrue();
});

it('can validate template variables', function () {
    $this->actingAs($this->adminUser);
    
    $template = EmailTemplate::factory()->create([
        'name' => 'Test Email',
        'subject' => 'Hello {user.first_name}!',
        'body' => '<p>Your email is {user.email} and you are a {user.profile_type}.</p>',
        'created_by' => $this->adminUser->id,
    ]);
    
    $response = $this->postJson('/admin/email-templates/validate', [
        'template_id' => $template->id,
        'context' => 'positive_athlete',
        'subject' => $template->subject,
        'body' => $template->body,
    ]);
    
    $response->assertOk();
    $data = $response->json();
    
    expect($data['valid'])->toBeTrue();
    expect($data['missingVariables'])->toBeEmpty();
    expect($data['allVariables'])->toContain('user.first_name');
    expect($data['allVariables'])->toContain('user.email');
    expect($data['allVariables'])->toContain('user.profile_type');
});

it('can detect missing variables', function () {
    $this->actingAs($this->adminUser);
    
    $response = $this->postJson('/admin/email-templates/validate', [
        'context' => 'positive_athlete',
        'subject' => 'Hello {user.first_name}!',
        'body' => '<p>Your sport is {user.sport} and your team is {user.team_name}.</p>',
    ]);
    
    $response->assertOk();
    $data = $response->json();
    
    expect($data['valid'])->toBeFalse();
    expect($data['missingVariables'])->toContain('user.sport');
    expect($data['missingVariables'])->toContain('user.team_name');
});

it('can send test email', function () {
    $this->actingAs($this->adminUser);
    
    $template = EmailTemplate::factory()->create([
        'name' => 'Test Email',
        'subject' => 'Hello {user.first_name}!',
        'body' => '<p>Welcome {user.first_name}!</p>',
        'created_by' => $this->adminUser->id,
    ]);
    
    $response = $this->postJson('/admin/email-templates/send-test', [
        'template_id' => $template->id,
        'context' => 'positive_athlete',
    ]);
    
    $response->assertOk();
    $data = $response->json();
    
    expect($data['success'])->toBeTrue();
    expect($data['message'])->toContain('Test email sent');
});
