<?php

use App\Models\EmailTemplate;
use App\Models\User;
use App\Services\VariableSubstitutionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create admin role if it doesn't exist
    Role::firstOrCreate(['name' => 'admin']);

    $this->adminUser = User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    $this->adminUser->assignRole('admin');
});

it('can generate context-specific sample data', function () {
    $service = app(VariableSubstitutionService::class);

    // Test positive athlete context
    $athleteData = $service->createSampleData('positive_athlete');
    expect($athleteData['user']['profile_type'])->toBe('positive_athlete');
    expect($athleteData['user']['first_name'])->toBe('Alex');
    expect($athleteData['user']['life_stage'])->toBe('high_school');

    // Test positive coach context
    $coachData = $service->createSampleData('positive_coach');
    expect($coachData['user']['profile_type'])->toBe('positive_coach');
    expect($coachData['user']['first_name'])->toBe('Sarah');
    expect($coachData['user']['life_stage'])->toBe('professional');
});

it('can highlight variables in template content', function () {
    $this->actingAs($this->adminUser);

    // Test the highlighting functionality
    $viewPage = new \App\Filament\Resources\EmailTemplateResource\Pages\ViewEmailTemplate();
    $reflection = new ReflectionClass($viewPage);
    $method = $reflection->getMethod('highlightVariables');
    $method->setAccessible(true);

    $highlighted = $method->invoke($viewPage, 'Hello {user.first_name}!');
    expect($highlighted)->toContain('<span class="variable-highlight">{user.first_name}</span>');
});

it('can extract variables from template', function () {
    $template = EmailTemplate::factory()->create([
        'name' => 'Test Email',
        'subject' => 'Hello {user.first_name}!',
        'body' => '<p>Your email is {user.email} and you are a {user.profile_type}.</p>',
        'created_by' => $this->adminUser->id,
    ]);

    $variables = $template->getVariables();

    expect($variables)->toContain('user.first_name');
    expect($variables)->toContain('user.email');
    expect($variables)->toContain('user.profile_type');
});
